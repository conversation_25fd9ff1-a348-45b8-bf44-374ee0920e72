#!/usr/bin/env python3
"""
Script per avviare il DCST Tool e testare le modifiche di plateau_count e reheat_count
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# Aggiungi la directory corrente al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import dell'app
from app.gui import App

def main():
    """Avvia l'applicazione DCST Tool"""
    print("🚀 Avvio DCST Tool...")
    print("📊 Test delle modifiche plateau_count e reheat_count")
    print("=" * 60)
    
    # Crea la finestra principale
    root = tk.Tk()
    
    # Crea la progress bar
    progress_bar = ttk.Progressbar(root, orient="horizontal", length=300, mode="determinate")
    
    # Crea l'app
    app = App(root, progress_bar)
    
    print("✅ GUI inizializzata con successo!")
    print("🔍 Verifica che nel pannello 'Parametri in tempo reale' siano presenti:")
    print("   - Temperatura")
    print("   - Iterazioni") 
    print("   - Costo attuale")
    print("   - Accettazioni")
    print("   - Plateau")
    print("   - Reheat")
    print()
    print("🎯 Per testare le modifiche:")
    print("   1. Clicca 'Avvia' per iniziare l'ottimizzazione")
    print("   2. Osserva i valori di Plateau e Reheat che si aggiornano in tempo reale")
    print("   3. Controlla i log per i messaggi '[SA] Plateau: X – Reheat: Y'")
    print("   4. Verifica i messaggi di reheating evidenziati")
    print()
    print("🔧 Parametri di default ottimali per il test:")
    print("   - Istanza piccola: 10 nodi")
    print("   - Istanza media: 50 nodi") 
    print("   - Istanza grande: 200 nodi")
    print("   - Max figli: 3")
    print("   - Penalità: 1000")
    print("   - Connessione: 0.30")
    print()
    
    try:
        # Avvia l'app
        root.mainloop()
        print("✅ App chiusa correttamente")
        
    except KeyboardInterrupt:
        print("\n⚠️  App interrotta dall'utente")
    except Exception as e:
        print(f"❌ Errore durante l'esecuzione: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("🏁 Test completato")

if __name__ == "__main__":
    main()
