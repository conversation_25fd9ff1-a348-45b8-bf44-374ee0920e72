# 📈 Implementazione Grafici Temporali Evoluzione Punteggio

## 🎯 Obiettivo Raggiunto

È stata implementata con successo la funzionalità per **generare grafici temporali live dell'evoluzione del punteggio** durante l'esecuzione degli algoritmi Local Search e Simulated Annealing. Questa funzionalità consente di confrontare visivamente il comportamento nel tempo dei diversi algoritmi.

## 🔧 Modifiche Implementate

### 1. **Algoritmi Modificati** (`app/algorithms.py`)

#### Local Search (`adaptive_neighborhood_local_search`)
- ✅ Aggiunta raccolta punteggi **ogni 5 iterazioni**
- ✅ Calcolo del punteggio usando `evaluate_solution()`
- ✅ Storico salvato in `score_history`
- ✅ Return modificato per includere `score_history`

#### Simulated Annealing (`simulated_annealing_spanning_tree`)
- ✅ Aggiunta raccolta punteggi **ogni 10 iterazioni**
- ✅ Calcolo del punteggio usando `evaluate_solution()`
- ✅ Storico salvato in `score_history`
- ✅ Return modificato per includere `score_history`

#### Parallel Local Search (`parallel_local_search`)
- ✅ Aggiornato per gestire il nuovo valore di ritorno
- ✅ Raccoglie il miglior `score_history` tra tutti i thread

#### Test Instance (`test_instance`)
- ✅ Aggiornato per gestire i nuovi valori di ritorno
- ✅ Salva gli storici nei risultati: `local_score_history`, `sa_score_history`

### 2. **Nuova Funzione di Plotting** (`app/utils.py`)

#### `plot_score_evolution(score_histories, filename)`
- ✅ Genera grafici temporali con curve per ogni algoritmo
- ✅ Colori distintivi per ogni algoritmo:
  - 🔴 **Simulated Annealing**: Rosso
  - 🔵 **Local Search**: Turchese
  - 🟢 **Greedy**: Blu (per future estensioni)
- ✅ Stili di linea diversi per varietà visiva
- ✅ Salvataggio automatico nella cartella `Plot` del Desktop
- ✅ Gestione errori e logging

### 3. **Integrazione GUI** (`app/gui.py`)

#### Funzione `save_results`
- ✅ Aggiunta generazione automatica dei grafici temporali
- ✅ Un grafico per ogni istanza (piccola, media, grande)
- ✅ Nomi file descrittivi con parametri inclusi
- ✅ Logging dettagliato del processo

#### Funzione `save_partial_results`
- ✅ Aggiunta generazione grafici temporali anche per risultati parziali
- ✅ Gestione interruzioni utente

## 📊 Risultati Ottenuti

### File Generati Automaticamente
Per ogni esecuzione vengono generati i seguenti grafici temporali:

```
📁 Desktop/Plot/
├── evoluzione_punteggio_istanzaPiccola_piccola_n10_k3_p0.30_pen1000.png
├── evoluzione_punteggio_istanzaMedia_media_n50_k3_p0.30_pen1000.png
└── evoluzione_punteggio_istanzaGrande_grande_n200_k3_p0.30_pen1000.png
```

### Caratteristiche dei Grafici
- 📈 **Asse X**: Numero di iterazioni
- 📈 **Asse Y**: Punteggio (più alto = migliore)
- 🎨 **Curve colorate** per ogni algoritmo
- 📊 **Griglia** per facilità di lettura
- 🏷️ **Legenda** con nomi algoritmi
- 📐 **Alta risoluzione** (300 DPI)

## 🧪 Test Implementati

### 1. **Test Base** (`test_score_evolution.py`)
- ✅ Verifica raccolta storici punteggi
- ✅ Test funzione `plot_score_evolution`
- ✅ Verifica generazione file

### 2. **Test Completo** (`test_full_score_evolution.py`)
- ✅ Test esecuzione completa con tutte le istanze
- ✅ Verifica integrazione GUI
- ✅ Test generazione multipli grafici

### 3. **Verifica File** (`verify_score_evolution_files.py`)
- ✅ Controllo esistenza file generati
- ✅ Verifica dimensioni file
- ✅ Lista file più recenti

## 📈 Esempio di Utilizzo

### Esecuzione Automatica
I grafici vengono generati automaticamente durante ogni esecuzione dell'applicazione:

1. **Avvia l'applicazione**: `python run.py`
2. **Configura parametri** nella GUI
3. **Avvia ottimizzazione**
4. **I grafici temporali vengono salvati automaticamente** nella cartella Plot

### Dati Raccolti
- **Local Search**: Punteggio ogni 5 iterazioni
- **Simulated Annealing**: Punteggio ogni 10 iterazioni
- **Calcolo punteggio**: Basato su costo, tempo, memoria, violazioni

## 🎉 Benefici della Funzionalità

### 1. **Analisi Visiva**
- 👁️ Confronto immediato del comportamento degli algoritmi
- 📊 Identificazione di plateau e miglioramenti
- 🔍 Analisi della convergenza

### 2. **Debugging e Ottimizzazione**
- 🐛 Identificazione di problemi negli algoritmi
- ⚡ Ottimizzazione dei parametri
- 📈 Valutazione delle prestazioni

### 3. **Presentazione Risultati**
- 📋 Grafici professionali per report
- 🎨 Visualizzazione chiara e comprensibile
- 📊 Documentazione automatica delle esecuzioni

## ✅ Stato Implementazione

| Componente | Stato | Note |
|------------|-------|------|
| Raccolta dati Local Search | ✅ Completato | Ogni 5 iterazioni |
| Raccolta dati Simulated Annealing | ✅ Completato | Ogni 10 iterazioni |
| Funzione plotting | ✅ Completato | Con colori e stili |
| Integrazione GUI | ✅ Completato | Automatica |
| Gestione risultati parziali | ✅ Completato | Su interruzione |
| Test e verifica | ✅ Completato | Tutti i test passano |
| Documentazione | ✅ Completato | Questo documento |

## 🚀 Prossimi Sviluppi Possibili

1. **Grafici Interattivi**: Implementazione con Plotly per zoom e interazione
2. **Metriche Aggiuntive**: Grafici per temperatura, accettazioni, violazioni
3. **Confronto Storico**: Salvataggio e confronto di esecuzioni precedenti
4. **Export Dati**: Esportazione dati in CSV per analisi esterne

---

**✨ La funzionalità è completamente operativa e pronta per l'uso!**
