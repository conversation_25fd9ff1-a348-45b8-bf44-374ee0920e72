#!/usr/bin/env python3
"""
Test per verificare la coerenza del calcolo delle violazioni dei vincoli di grado
per tutti gli algoritmi (Greedy, Local Search, Simulated Annealing).
"""

import sys
import os
import logging

# Aggiungi la directory corrente al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.utils import generate_connected_random_graph
from app.algorithms import (
    greedy_spanning_tree, 
    adaptive_neighborhood_local_search,
    simulated_annealing_spanning_tree,
    count_constraint_violations,
    get_violating_nodes,
    test_violations_consistency
)

def test_violations_calculation():
    """Test completo del calcolo delle violazioni"""
    
    print("🧪 TEST CALCOLO VIOLAZIONI DEI VINCOLI DI GRADO")
    print("=" * 60)
    
    # Configura logging per debug
    logging.basicConfig(level=logging.DEBUG)
    
    # Parametri di test
    test_cases = [
        {"nodes": 8, "max_children": 2, "penalty": 1000, "p": 0.4},
        {"nodes": 12, "max_children": 3, "penalty": 1000, "p": 0.35},
        {"nodes": 15, "max_children": 4, "penalty": 1000, "p": 0.3}
    ]
    
    all_tests_passed = True
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📋 Test Case {i}: {case['nodes']} nodi, max_children={case['max_children']}")
        print("-" * 40)
        
        try:
            # Genera grafo di test
            G = generate_connected_random_graph(case['nodes'], case['p'])
            print(f"✅ Grafo generato: {len(G.nodes())} nodi, {len(G.edges())} archi")
            
            # Test Greedy
            print("🔍 Test Greedy Algorithm...")
            greedy_tree, greedy_cost = greedy_spanning_tree(G, case['max_children'], case['penalty'])
            greedy_violations = count_constraint_violations(greedy_tree, case['max_children'])
            
            if test_violations_consistency(greedy_tree, case['max_children']):
                print(f"✅ Greedy: {greedy_violations} violazioni - Calcolo coerente")
            else:
                print(f"❌ Greedy: Calcolo NON coerente!")
                all_tests_passed = False
            
            # Test Local Search
            print("🔍 Test Local Search Algorithm...")
            local_tree, _ = adaptive_neighborhood_local_search(
                G, greedy_tree, case['max_children'], case['penalty'], max_iterations=100
            )
            local_violations = count_constraint_violations(local_tree, case['max_children'])
            
            if test_violations_consistency(local_tree, case['max_children']):
                print(f"✅ Local Search: {local_violations} violazioni - Calcolo coerente")
            else:
                print(f"❌ Local Search: Calcolo NON coerente!")
                all_tests_passed = False
            
            # Test Simulated Annealing
            print("🔍 Test Simulated Annealing Algorithm...")
            sa_tree, sa_cost, sa_iterations, sa_accepts = simulated_annealing_spanning_tree(
                G, case['max_children'], case['penalty'], 
                max_iterations=200, initial_tree=local_tree
            )
            sa_violations = count_constraint_violations(sa_tree, case['max_children'])
            
            if test_violations_consistency(sa_tree, case['max_children']):
                print(f"✅ Simulated Annealing: {sa_violations} violazioni - Calcolo coerente")
            else:
                print(f"❌ Simulated Annealing: Calcolo NON coerente!")
                all_tests_passed = False
            
            # Confronto risultati
            print(f"\n📊 Riepilogo Test Case {i}:")
            print(f"   • Greedy: costo={greedy_cost}, violazioni={greedy_violations}")
            print(f"   • Local Search: violazioni={local_violations}")
            print(f"   • Simulated Annealing: costo={sa_cost}, violazioni={sa_violations}")
            
            # Verifica che Local Search e SA abbiano migliorato o mantenuto le violazioni
            if local_violations <= greedy_violations:
                print(f"✅ Local Search ha migliorato/mantenuto le violazioni")
            else:
                print(f"⚠️  Local Search ha peggiorato le violazioni")
            
            if sa_violations <= local_violations:
                print(f"✅ Simulated Annealing ha migliorato/mantenuto le violazioni")
            else:
                print(f"⚠️  Simulated Annealing ha peggiorato le violazioni")
                
        except Exception as e:
            print(f"❌ Errore nel Test Case {i}: {e}")
            import traceback
            traceback.print_exc()
            all_tests_passed = False
    
    # Test specifico per funzioni di utilità
    print(f"\n🔧 Test Funzioni di Utilità...")
    print("-" * 40)
    
    try:
        # Crea un grafo di test semplice
        import networkx as nx
        test_graph = nx.Graph()
        test_graph.add_edges_from([(0, 1), (0, 2), (0, 3), (0, 4), (1, 5)], weight=1)
        
        # Nodo 0 ha 4 figli, con max_children=2 dovrebbe essere una violazione
        violations = count_constraint_violations(test_graph, 2)
        violating_nodes = get_violating_nodes(test_graph, 2)
        
        print(f"✅ Test grafo semplice: {violations} violazioni")
        print(f"✅ Nodi violanti: {violating_nodes}")
        
        if test_violations_consistency(test_graph, 2):
            print(f"✅ Funzioni di utilità: Calcolo coerente")
        else:
            print(f"❌ Funzioni di utilità: Calcolo NON coerente!")
            all_tests_passed = False
            
    except Exception as e:
        print(f"❌ Errore nel test delle funzioni di utilità: {e}")
        all_tests_passed = False
    
    # Risultato finale
    print(f"\n{'🎉 TUTTI I TEST SUPERATI!' if all_tests_passed else '❌ ALCUNI TEST FALLITI'}")
    print("=" * 60)
    
    if all_tests_passed:
        print("✅ Il calcolo delle violazioni è coerente per tutti gli algoritmi")
        print("✅ Le funzioni centralizzate funzionano correttamente")
        print("✅ Non ci sono duplicazioni di codice problematiche")
    else:
        print("❌ Sono state rilevate inconsistenze nel calcolo delle violazioni")
        print("❌ Verificare l'implementazione delle funzioni centralizzate")
    
    return all_tests_passed

if __name__ == "__main__":
    success = test_violations_calculation()
    sys.exit(0 if success else 1)
