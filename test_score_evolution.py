#!/usr/bin/env python3
"""
Test script per verificare la funzionalità di evoluzione del punteggio.
Questo script testa la nuova funzionalità di generazione dei grafici temporali
dell'evoluzione del punteggio durante l'esecuzione degli algoritmi.
"""

import os
import sys
import time
import queue
import logging

# Aggiungi il percorso del progetto al PYTHONPATH
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_score_evolution():
    """Test della funzionalità di evoluzione del punteggio."""
    
    print("🔥 Test della funzionalità di evoluzione del punteggio")
    print("=" * 60)
    
    try:
        # Import delle funzioni necessarie
        from app.utils import generate_connected_random_graph, plot_score_evolution
        from app.algorithms import test_instance
        
        print("✅ Import delle funzioni riuscito")
        
        # Genera un grafo di test di dimensioni moderate
        print("📊 Generazione grafo di test...")
        G = generate_connected_random_graph(15, 0.4)
        print(f"✅ Grafo generato: {len(G.nodes())} nodi, {len(G.edges())} archi")
        
        # Parametri di test
        max_children = 3
        penalty = 1000
        
        # Crea una coda per i messaggi
        test_queue = queue.Queue()
        
        print(f"🔥 Esecuzione test_instance con max_children={max_children}...")
        print("   Questo test raccoglierà gli storici dei punteggi durante l'esecuzione")
        
        # Esegui test_instance
        start_time = time.time()
        results = test_instance(
            G=G,
            max_children=max_children,
            penalty=penalty,
            instance_name="test_evolution",
            stop_event=None,
            queue=test_queue,
            progress_info=None
        )
        end_time = time.time()
        
        print(f"✅ Test completato in {end_time - start_time:.2f} secondi")
        
        # Verifica che i risultati contengano gli storici dei punteggi
        print("\n📈 Verifica degli storici dei punteggi:")
        
        # Controlla Local Search
        if "local_score_history" in results:
            local_history = results["local_score_history"]
            print(f"✅ Local Search: {len(local_history)} punti dati raccolti")
            if local_history:
                print(f"   Primo punto: iterazione {local_history[0][0]}, punteggio {local_history[0][1]:.2f}")
                print(f"   Ultimo punto: iterazione {local_history[-1][0]}, punteggio {local_history[-1][1]:.2f}")
        else:
            print("❌ Local Search: nessuno storico trovato")
        
        # Controlla Simulated Annealing
        if "sa_score_history" in results:
            sa_history = results["sa_score_history"]
            print(f"✅ Simulated Annealing: {len(sa_history)} punti dati raccolti")
            if sa_history:
                print(f"   Primo punto: iterazione {sa_history[0][0]}, punteggio {sa_history[0][1]:.2f}")
                print(f"   Ultimo punto: iterazione {sa_history[-1][0]}, punteggio {sa_history[-1][1]:.2f}")
        else:
            print("❌ Simulated Annealing: nessuno storico trovato")
        
        # Test della funzione di plotting
        print("\n🎨 Test della funzione plot_score_evolution:")
        
        score_histories = {}
        
        if "local_score_history" in results and results["local_score_history"]:
            score_histories["Local Search"] = results["local_score_history"]
        
        if "sa_score_history" in results and results["sa_score_history"]:
            score_histories["Simulated Annealing"] = results["sa_score_history"]
        
        if score_histories:
            print(f"📊 Generazione grafico con {len(score_histories)} algoritmi...")
            
            # Genera il grafico
            filename = "test_score_evolution.png"
            success = plot_score_evolution(score_histories, filename)
            
            if success:
                print(f"✅ Grafico generato con successo: {filename}")
                
                # Verifica che il file esista
                desktop_path = os.path.join(os.path.expanduser("~"), "Desktop", "Plot")
                full_path = os.path.join(desktop_path, filename)
                if os.path.exists(full_path):
                    print(f"✅ File verificato: {full_path}")
                    file_size = os.path.getsize(full_path)
                    print(f"   Dimensione file: {file_size} bytes")
                else:
                    print(f"❌ File non trovato: {full_path}")
            else:
                print("❌ Errore nella generazione del grafico")
        else:
            print("❌ Nessuno storico disponibile per il plotting")
        
        # Stampa un riepilogo dei risultati
        print("\n📊 Riepilogo risultati algoritmi:")
        for algo in ["greedy", "local", "sa"]:
            if f"{algo}_cost" in results:
                cost = results[f"{algo}_cost"]
                time_taken = results[f"{algo}_time"]
                violations = results.get(f"{algo}_violations", 0)
                calls = results.get(f"{algo}_calls", 0)
                print(f"   {algo.upper()}: Costo={cost}, Tempo={time_taken:.4f}s, Violazioni={violations}, Chiamate={calls}")
        
        print("\n🎉 Test completato con successo!")
        return True
        
    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_plot_function_directly():
    """Test diretto della funzione plot_score_evolution con dati simulati."""
    
    print("\n🧪 Test diretto della funzione plot_score_evolution")
    print("=" * 60)
    
    try:
        from app.utils import plot_score_evolution
        
        # Crea dati simulati per il test
        simulated_histories = {
            "Local Search": [
                (0, 85.0), (5, 87.2), (10, 89.1), (15, 90.5), 
                (20, 91.8), (25, 92.3), (30, 92.7), (35, 93.0)
            ],
            "Simulated Annealing": [
                (0, 82.0), (10, 84.5), (20, 88.2), (30, 91.0), 
                (40, 93.5), (50, 95.1), (60, 96.2), (70, 96.8),
                (80, 97.0), (90, 97.2), (100, 97.5)
            ]
        }
        
        print("📊 Generazione grafico con dati simulati...")
        filename = "test_simulated_evolution.png"
        success = plot_score_evolution(simulated_histories, filename)
        
        if success:
            print(f"✅ Grafico simulato generato con successo: {filename}")
        else:
            print("❌ Errore nella generazione del grafico simulato")
        
        return success
        
    except Exception as e:
        print(f"❌ Errore nel test diretto: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Configura il logging
    logging.basicConfig(level=logging.INFO)
    
    print("🚀 Avvio test della funzionalità di evoluzione del punteggio")
    print("=" * 80)
    
    # Esegui i test
    test1_success = test_score_evolution()
    test2_success = test_plot_function_directly()
    
    print("\n" + "=" * 80)
    print("📋 RISULTATI FINALI:")
    print(f"   Test completo: {'✅ SUCCESSO' if test1_success else '❌ FALLITO'}")
    print(f"   Test diretto:  {'✅ SUCCESSO' if test2_success else '❌ FALLITO'}")
    
    if test1_success and test2_success:
        print("\n🎉 TUTTI I TEST SONO STATI SUPERATI!")
        print("   La funzionalità di evoluzione del punteggio è operativa.")
    else:
        print("\n⚠️  ALCUNI TEST SONO FALLITI")
        print("   Controllare i messaggi di errore sopra.")
    
    print("=" * 80)
