#!/usr/bin/env python3
"""
Test script to verify that plateau_count and reheat_count are properly displayed
in the GUI during Simulated Annealing execution.
"""

import sys
import os
import time
import queue
import threading
import tkinter as tk
from tkinter import ttk

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Import the GUI components
from app.gui import App

def test_plateau_reheat_display():
    """Test function to simulate plateau and reheat messages"""
    
    # Create a test window
    root = tk.Tk()
    root.title("Test Plateau/Reheat Display")
    root.geometry("500x800")
    
    # Create progress bar
    progress_bar = ttk.Progressbar(root, orient="horizontal", length=300, mode="determinate")
    
    # Create the app
    app = App(root, progress_bar)
    
    def simulate_sa_messages():
        """Simulate Simulated Annealing messages with plateau and reheat data"""
        time.sleep(2)  # Wait for GUI to initialize
        
        # Simulate SA execution with increasing plateau count and occasional reheats
        for iteration in range(0, 1000, 10):
            if not app.queue_running:
                break
                
            # Calculate simulated values
            temperature = max(0.1, 100 * (0.99 ** iteration))
            cost = 1000 + (iteration % 50) * 10  # Simulate cost fluctuations
            plateau_count = min(iteration // 10, 250)  # Plateau increases over time
            reheat_count = iteration // 200  # Reheat every 200 iterations
            accepted_count = iteration // 5  # Some acceptances
            
            # Send messages to queue
            app.queue.put(("temperature", round(temperature, 2)))
            app.queue.put(("iteration", iteration))
            app.queue.put(("cost", cost))
            app.queue.put(("plateau", plateau_count))
            app.queue.put(("reheats", reheat_count))
            app.queue.put(("accepted", accepted_count))
            
            # Log plateau and reheat status periodically
            if iteration % 100 == 0:
                app.queue.put(("log", (f"[SA] Plateau: {plateau_count} – Reheat: {reheat_count}", "info")))
            
            # Simulate reheating event
            if iteration > 0 and iteration % 200 == 0:
                app.queue.put(("log", (f"Reheating applied (#{reheat_count}): New temperature = {temperature:.2f}", "highlight")))
            
            time.sleep(0.1)  # Simulate real-time updates
        
        # Final message
        app.queue.put(("log", ("Test simulation completed!", "success")))
    
    # Start simulation in a separate thread
    sim_thread = threading.Thread(target=simulate_sa_messages, daemon=True)
    sim_thread.start()
    
    # Add instructions to the GUI
    instructions = tk.Label(
        root, 
        text="Test: Plateau and Reheat Display\n\nWatch the 'Parametri in tempo reale' section.\nYou should see:\n- Plateau count increasing\n- Reheat count incrementing\n- Log messages with plateau/reheat info",
        bg="#2b2b2b", 
        fg="white",
        justify="left",
        wraplength=400
    )
    instructions.pack(pady=10)
    
    # Run the GUI
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("Test interrupted by user")
    finally:
        app.queue_running = False

if __name__ == "__main__":
    print("Starting Plateau/Reheat Display Test...")
    print("This test will simulate Simulated Annealing execution")
    print("and show plateau_count and reheat_count in real-time.")
    print("Close the window to end the test.")
    print("-" * 50)
    
    test_plateau_reheat_display()
    
    print("Test completed!")
