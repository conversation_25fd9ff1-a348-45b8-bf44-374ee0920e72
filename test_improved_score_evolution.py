#!/usr/bin/env python3
"""
Test per verificare i miglioramenti implementati nei grafici temporali:
- Normalizzazione rispetto ai valori finali
- Smoothing con rolling average
- Asse Y ottimizzato
- Annotazioni per punti chiave
- Secondo asse per costo/violazioni
"""

import os
import sys
import time
import queue
import logging

# Aggiungi il percorso del progetto al PYTHONPATH
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_improved_plotting():
    """Test dei miglioramenti nella funzione plot_score_evolution."""

    print("🎨 Test miglioramenti grafici temporali")
    print("=" * 60)

    try:
        from app.utils import plot_score_evolution

        print("✅ Import della funzione migliorata riuscito")

        # Crea dati simulati con formato nuovo (dati completi)
        print("📊 Creazione dati simulati con formato migliorato...")

        # Simula dati Local Search con variazioni piccole ma significative
        local_search_data = []
        base_cost = 100
        for i in range(0, 50, 5):  # Ogni 5 iterazioni
            cost = base_cost - (i * 0.5)  # Miglioramento graduale
            violations = max(0, 5 - i // 10)  # Riduzione violazioni
            time_elapsed = i * 0.1

            score_data = {
                "cost": cost,
                "execution_time": time_elapsed,
                "memory": 1024 + i * 10,
                "violations": violations
            }
            local_search_data.append((i, score_data))

        # Simula dati Simulated Annealing con oscillazioni e convergenza
        sa_data = []
        base_cost = 110
        for i in range(0, 100, 10):  # Ogni 10 iterazioni
            # Oscillazioni iniziali poi convergenza
            if i < 30:
                cost = base_cost + random_oscillation(i)
                violations = 6 + (i % 3)
            else:
                cost = base_cost - (i - 30) * 0.8  # Miglioramento più rapido
                violations = max(0, 3 - (i - 30) // 15)

            time_elapsed = i * 0.15

            score_data = {
                "cost": cost,
                "execution_time": time_elapsed,
                "memory": 1200 + i * 8,
                "violations": violations
            }
            sa_data.append((i, score_data))

        # Crea gli storici con il nuovo formato
        score_histories = {
            "Local Search": local_search_data,
            "Simulated Annealing": sa_data
        }

        # Calcola i valori di riferimento finali (come farebbe l'applicazione reale)
        all_costs = [95, 92]  # Costi finali simulati
        all_times = [4.5, 13.5]  # Tempi finali simulati
        all_memories = [1500, 2000]  # Memorie finali simulate
        all_violations = [0, 0]  # Violazioni finali simulate

        reference_final_values = {
            "max_cost": max(all_costs),
            "max_time": max(all_times),
            "max_memory": max(all_memories),
            "max_violations": max(all_violations) or 1
        }

        print(f"📈 Valori di riferimento finali:")
        print(f"   Max cost: {reference_final_values['max_cost']}")
        print(f"   Max time: {reference_final_values['max_time']}")
        print(f"   Max memory: {reference_final_values['max_memory']}")
        print(f"   Max violations: {reference_final_values['max_violations']}")

        # Test della funzione migliorata
        print("\n🎨 Test funzione plot_score_evolution migliorata...")
        filename = "test_improved_evolution.png"

        success = plot_score_evolution(score_histories, reference_final_values, filename)

        if success:
            print(f"✅ Grafico migliorato generato: {filename}")

            # Verifica che il file esista e abbia dimensioni ragionevoli
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop", "Plot")
            full_path = os.path.join(desktop_path, filename)

            if os.path.exists(full_path):
                file_size = os.path.getsize(full_path)
                print(f"✅ File verificato: {file_size:,} bytes")

                if file_size > 50000:  # Almeno 50KB per un grafico complesso
                    print("✅ Dimensione file appropriata per grafico migliorato")
                else:
                    print("⚠️  File più piccolo del previsto")
            else:
                print(f"❌ File non trovato: {full_path}")
                return False
        else:
            print("❌ Errore nella generazione del grafico migliorato")
            return False

        # Test compatibilità con formato vecchio
        print("\n🔄 Test compatibilità con formato vecchio...")
        old_format_histories = {
            "Local Search": [(0, 85.0), (5, 87.2), (10, 89.1), (15, 90.5)],
            "Simulated Annealing": [(0, 82.0), (10, 84.5), (20, 88.2), (30, 91.0)]
        }

        filename_old = "test_backward_compatibility.png"
        success_old = plot_score_evolution(old_format_histories, None, filename_old)

        if success_old:
            print(f"✅ Compatibilità con formato vecchio: {filename_old}")
        else:
            print("❌ Errore nella compatibilità con formato vecchio")
            return False

        print("\n🎉 Tutti i test dei miglioramenti superati!")
        return True

    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()
        return False

def random_oscillation(iteration):
    """Simula oscillazioni casuali per il Simulated Annealing."""
    import random
    return random.uniform(-5, 5) * (1 - iteration / 30)  # Oscillazioni che diminuiscono

def test_real_execution_improved():
    """Test con esecuzione reale degli algoritmi per verificare i miglioramenti."""

    print("\n🔥 Test esecuzione reale con miglioramenti")
    print("=" * 60)

    try:
        from app.utils import generate_connected_random_graph
        from app.algorithms import test_instance

        print("✅ Import delle funzioni riuscito")

        # Genera un grafo di test piccolo per velocità
        print("📊 Generazione grafo di test...")
        G = generate_connected_random_graph(12, 0.4)
        print(f"✅ Grafo generato: {len(G.nodes())} nodi, {len(G.edges())} archi")

        # Parametri di test
        max_children = 3
        penalty = 1000

        # Crea una coda per i messaggi
        test_queue = queue.Queue()

        print(f"🔥 Esecuzione test_instance...")

        # Esegui test_instance
        start_time = time.time()
        results = test_instance(
            G=G,
            max_children=max_children,
            penalty=penalty,
            instance_name="test_improved",
            stop_event=None,
            queue=test_queue,
            progress_info=None
        )
        end_time = time.time()

        print(f"✅ Test completato in {end_time - start_time:.2f} secondi")

        # Verifica che i risultati contengano dati nel nuovo formato
        print("\n📈 Verifica formato dati migliorato:")

        local_history = results.get("local_score_history", [])
        sa_history = results.get("sa_score_history", [])

        print(f"✅ Local Search: {len(local_history)} punti dati")
        print(f"✅ Simulated Annealing: {len(sa_history)} punti dati")

        # Verifica che i dati siano nel formato corretto (iter, dict)
        if local_history and len(local_history) > 0:
            first_item = local_history[0]
            if isinstance(first_item, tuple) and len(first_item) == 2:
                iteration, data = first_item
                if isinstance(data, dict) and "cost" in data:
                    print("✅ Formato dati Local Search corretto (iter, dict)")
                else:
                    print("❌ Formato dati Local Search non corretto")
                    return False

        if sa_history and len(sa_history) > 0:
            first_item = sa_history[0]
            if isinstance(first_item, tuple) and len(first_item) == 2:
                iteration, data = first_item
                if isinstance(data, dict) and "cost" in data:
                    print("✅ Formato dati SA corretto (iter, dict)")
                else:
                    print("❌ Formato dati SA non corretto")
                    return False

        # Test del grafico con dati reali
        print("\n🎨 Test grafico con dati reali migliorati...")

        from app.utils import plot_score_evolution

        score_histories = {
            "Local Search": local_history,
            "Simulated Annealing": sa_history
        }

        # Calcola valori di riferimento reali
        reference_final_values = {
            "max_cost": max(results.get("greedy_cost", 1), results.get("local_cost", 1), results.get("sa_cost", 1)),
            "max_time": max(results.get("greedy_time", 1), results.get("local_time", 1), results.get("sa_time", 1)),
            "max_memory": max(results.get("greedy_memory", 1), results.get("local_memory", 1), results.get("sa_memory", 1)),
            "max_violations": max(results.get("greedy_violations", 1), results.get("local_violations", 1), results.get("sa_violations", 1))
        }

        filename = "test_real_improved_evolution.png"
        success = plot_score_evolution(score_histories, reference_final_values, filename)

        if success:
            print(f"✅ Grafico con dati reali generato: {filename}")
        else:
            print("❌ Errore nella generazione del grafico con dati reali")
            return False

        print("\n🎉 Test esecuzione reale completato con successo!")
        return True

    except Exception as e:
        print(f"❌ Errore durante il test reale: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Configura il logging
    logging.basicConfig(level=logging.INFO)

    print("🚀 Test miglioramenti grafici temporali evoluzione punteggio")
    print("=" * 80)

    # Esegui i test
    test1_success = test_improved_plotting()
    test2_success = test_real_execution_improved()

    print("\n" + "=" * 80)
    print("📋 RISULTATI FINALI:")
    print(f"   Test miglioramenti plotting: {'✅ SUCCESSO' if test1_success else '❌ FALLITO'}")
    print(f"   Test esecuzione reale:       {'✅ SUCCESSO' if test2_success else '❌ FALLITO'}")

    if test1_success and test2_success:
        print("\n🎉 TUTTI I TEST DEI MIGLIORAMENTI SUPERATI!")
        print("   I grafici temporali sono ora più chiari e leggibili con:")
        print("   ✅ Normalizzazione rispetto ai valori finali")
        print("   ✅ Smoothing con rolling average")
        print("   ✅ Asse Y ottimizzato per variazioni piccole")
        print("   ✅ Annotazioni per punti chiave")
        print("   ✅ Secondo asse per costo/violazioni")
        print("   ✅ Compatibilità con formato precedente")
    else:
        print("\n⚠️  ALCUNI TEST SONO FALLITI")
        print("   Controllare i messaggi di errore sopra.")

    print("=" * 80)
