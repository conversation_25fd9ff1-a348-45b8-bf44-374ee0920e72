#!/usr/bin/env python3
"""
Test dell'app completa per verificare il calcolo delle violazioni
"""

import sys
import os
import queue
import time

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_app_violations():
    """Test dell'app completa con focus sulle violazioni"""
    
    print("🚀 TEST APP COMPLETA - CALCOLO VIOLAZIONI")
    print("=" * 60)
    
    try:
        from app.utils import generate_connected_random_graph
        from app.algorithms import test_instance
        
        print("✅ Import delle funzioni riuscito")
        
        # Genera un grafo di test
        G = generate_connected_random_graph(12, 0.4)
        print(f"✅ Grafo generato: {len(G.nodes())} nodi, {len(G.edges())} archi")
        
        # Parametri di test
        max_children = 3
        penalty = 1000
        
        # Crea una coda per i messaggi
        test_queue = queue.Queue()
        
        print(f"🔥 Esecuzione test_instance con max_children={max_children}...")
        
        # Esegui test_instance
        results = test_instance(
            G=G,
            max_children=max_children,
            penalty=penalty,
            instance_name="test",
            queue=test_queue
        )
        
        print("✅ test_instance completato")
        
        # Analizza i risultati
        algorithms = ["greedy", "local", "sa"]
        
        print(f"\n📊 RISULTATI VIOLAZIONI:")
        print("-" * 40)
        
        for algo in algorithms:
            if f"{algo}_violations" in results:
                violations = results[f"{algo}_violations"]
                cost = results[f"{algo}_cost"]
                time_taken = results[f"{algo}_time"]
                calls = results[f"{algo}_calls"]
                
                print(f"• {algo.upper():15}: {violations:2d} violazioni, costo={cost:6.0f}, tempo={time_taken:.4f}s, chiamate={calls}")
            else:
                print(f"• {algo.upper():15}: DATI MANCANTI")
        
        # Verifica che tutti gli algoritmi abbiano i dati delle violazioni
        missing_data = []
        for algo in algorithms:
            if f"{algo}_violations" not in results:
                missing_data.append(algo)
        
        if missing_data:
            print(f"❌ Dati violazioni mancanti per: {missing_data}")
            return False
        
        # Verifica che le violazioni siano numeri validi
        for algo in algorithms:
            violations = results[f"{algo}_violations"]
            if not isinstance(violations, int) or violations < 0:
                print(f"❌ Violazioni non valide per {algo}: {violations}")
                return False
        
        # Analizza i messaggi dalla coda
        log_messages = []
        while not test_queue.empty():
            try:
                msg_type, msg_value = test_queue.get_nowait()
                if msg_type == "log":
                    log_msg, log_level = msg_value
                    if "violazioni=" in log_msg:
                        log_messages.append(log_msg)
            except queue.Empty:
                break
        
        print(f"\n📝 MESSAGGI LOG CON VIOLAZIONI:")
        print("-" * 40)
        for msg in log_messages:
            print(f"• {msg}")
        
        if len(log_messages) >= 3:  # Dovremmo avere almeno un messaggio per algoritmo
            print("✅ Log delle violazioni presenti")
        else:
            print("⚠️  Pochi log delle violazioni trovati")
        
        # Verifica che Local Search e SA abbiano migliorato o mantenuto le violazioni
        greedy_violations = results["greedy_violations"]
        local_violations = results["local_violations"]
        sa_violations = results["sa_violations"]
        
        print(f"\n🔍 ANALISI MIGLIORAMENTI:")
        print("-" * 40)
        
        if local_violations <= greedy_violations:
            print(f"✅ Local Search: {greedy_violations} → {local_violations} (migliorato/mantenuto)")
        else:
            print(f"⚠️  Local Search: {greedy_violations} → {local_violations} (peggiorato)")
        
        if sa_violations <= local_violations:
            print(f"✅ Simulated Annealing: {local_violations} → {sa_violations} (migliorato/mantenuto)")
        else:
            print(f"⚠️  Simulated Annealing: {local_violations} → {sa_violations} (peggiorato)")
        
        print(f"\n🎉 TEST COMPLETATO CON SUCCESSO!")
        print("✅ Tutti gli algoritmi calcolano correttamente le violazioni")
        print("✅ I dati sono presenti nei risultati")
        print("✅ I log mostrano le violazioni")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_app_violations()
    print(f"\n{'🏆 SUCCESSO!' if success else '💥 FALLIMENTO!'}")
    sys.exit(0 if success else 1)
