#!/usr/bin/env python3
"""
Test semplice per verificare le funzioni di calcolo delle violazioni
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_violations():
    """Test base delle funzioni di violazione"""
    
    print("🧪 TEST SEMPLICE CALCOLO VIOLAZIONI")
    print("=" * 50)
    
    try:
        # Import delle funzioni
        from app.algorithms import count_constraint_violations, get_violating_nodes, test_violations_consistency
        import networkx as nx
        
        print("✅ Import delle funzioni riuscito")
        
        # Crea un grafo di test semplice
        G = nx.Graph()
        # Nodo 0 connesso a 4 nodi (1,2,3,4) - con max_children=2 dovrebbe violare
        G.add_edges_from([(0, 1), (0, 2), (0, 3), (0, 4)], weight=1)
        # Nodo 1 connesso solo a 0 - nessuna violazione
        # Aggiungiamo un altro nodo che viola
        G.add_edges_from([(5, 6), (5, 7), (5, 8)], weight=1)  # Nodo 5 con 3 figli
        
        print(f"✅ Grafo di test creato: {len(G.nodes())} nodi, {len(G.edges())} archi")
        
        # Test con max_children = 2
        max_children = 2
        
        # Test count_constraint_violations
        violations = count_constraint_violations(G, max_children)
        print(f"✅ count_constraint_violations: {violations} violazioni")
        
        # Test get_violating_nodes
        violating_nodes = get_violating_nodes(G, max_children)
        print(f"✅ get_violating_nodes: {violating_nodes}")
        
        # Test test_violations_consistency
        is_consistent = test_violations_consistency(G, max_children)
        print(f"✅ test_violations_consistency: {'COERENTE' if is_consistent else 'NON COERENTE'}")
        
        # Verifica manuale
        expected_violations = 2  # Nodi 0 e 5 dovrebbero violare
        expected_nodes = [0, 5]
        
        if violations == expected_violations:
            print(f"✅ Numero violazioni corretto: {violations}")
        else:
            print(f"❌ Numero violazioni errato: atteso {expected_violations}, ottenuto {violations}")
            return False
        
        if set(violating_nodes) == set(expected_nodes):
            print(f"✅ Nodi violanti corretti: {violating_nodes}")
        else:
            print(f"❌ Nodi violanti errati: attesi {expected_nodes}, ottenuti {violating_nodes}")
            return False
        
        if is_consistent:
            print(f"✅ Calcolo coerente")
        else:
            print(f"❌ Calcolo NON coerente")
            return False
        
        print("\n🎉 TUTTI I TEST SUPERATI!")
        return True
        
    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_basic_violations()
    print(f"\n{'✅ TEST COMPLETATO CON SUCCESSO' if success else '❌ TEST FALLITO'}")
    sys.exit(0 if success else 1)
