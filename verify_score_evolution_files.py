#!/usr/bin/env python3
"""
Script per verificare che i file dei grafici temporali siano stati creati correttamente.
"""

import os
import glob

def verify_score_evolution_files():
    """Verifica che i file dei grafici temporali siano stati creati."""
    
    print("🔍 Verifica dei file grafici temporali")
    print("=" * 60)
    
    # Percorso della cartella Plot
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    plot_dir = os.path.join(desktop_path, "Plot")
    
    print(f"📁 Cartella Plot: {plot_dir}")
    
    if not os.path.exists(plot_dir):
        print("❌ Cartella Plot non trovata!")
        return False
    
    # Cerca tutti i file di evoluzione del punteggio
    evolution_pattern = os.path.join(plot_dir, "*evoluzione_punteggio*.png")
    evolution_files = glob.glob(evolution_pattern)
    
    print(f"\n📊 File di evoluzione del punteggio trovati: {len(evolution_files)}")
    
    if not evolution_files:
        print("❌ Nessun file di evoluzione del punteggio trovato!")
        return False
    
    # Verifica ogni file
    total_size = 0
    for file_path in evolution_files:
        filename = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        total_size += file_size
        
        print(f"   ✅ {filename}")
        print(f"      Dimensione: {file_size:,} bytes")
        
        # Verifica che il file non sia vuoto
        if file_size == 0:
            print(f"      ❌ File vuoto!")
            return False
        elif file_size < 1000:
            print(f"      ⚠️  File molto piccolo, potrebbe essere corrotto")
        else:
            print(f"      ✅ Dimensione OK")
    
    print(f"\n📈 Riepilogo:")
    print(f"   File totali: {len(evolution_files)}")
    print(f"   Dimensione totale: {total_size:,} bytes ({total_size/1024:.1f} KB)")
    
    # Verifica che ci siano file per le diverse istanze
    instances_found = set()
    for file_path in evolution_files:
        filename = os.path.basename(file_path)
        if "Piccola" in filename:
            instances_found.add("piccola")
        elif "Media" in filename:
            instances_found.add("media")
        elif "Grande" in filename:
            instances_found.add("grande")
    
    print(f"\n🏷️  Istanze trovate: {', '.join(instances_found)}")
    
    expected_instances = {"piccola", "media", "grande"}
    if instances_found == expected_instances:
        print("✅ Tutte le istanze hanno i loro grafici temporali!")
    else:
        missing = expected_instances - instances_found
        print(f"⚠️  Istanze mancanti: {', '.join(missing)}")
    
    # Cerca anche i file di test
    test_pattern = os.path.join(plot_dir, "*test*evolution*.png")
    test_files = glob.glob(test_pattern)
    
    if test_files:
        print(f"\n🧪 File di test trovati: {len(test_files)}")
        for file_path in test_files:
            filename = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            print(f"   ✅ {filename} ({file_size:,} bytes)")
    
    # Lista tutti i file PNG nella cartella per completezza
    all_png_files = glob.glob(os.path.join(plot_dir, "*.png"))
    print(f"\n📁 Tutti i file PNG nella cartella Plot: {len(all_png_files)}")
    
    evolution_count = len([f for f in all_png_files if "evoluzione" in os.path.basename(f)])
    other_count = len(all_png_files) - evolution_count
    
    print(f"   📊 Grafici evoluzione: {evolution_count}")
    print(f"   📊 Altri grafici: {other_count}")
    
    if evolution_count >= 3:  # Almeno 3 istanze
        print("\n🎉 SUCCESSO! I grafici temporali sono stati generati correttamente!")
        return True
    else:
        print(f"\n⚠️  ATTENZIONE: Solo {evolution_count} grafici di evoluzione trovati (attesi almeno 3)")
        return False

def show_recent_files():
    """Mostra i file più recenti nella cartella Plot."""
    
    print("\n🕒 File più recenti nella cartella Plot:")
    print("=" * 60)
    
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    plot_dir = os.path.join(desktop_path, "Plot")
    
    if not os.path.exists(plot_dir):
        print("❌ Cartella Plot non trovata!")
        return
    
    # Ottieni tutti i file PNG e ordinali per data di modifica
    png_files = glob.glob(os.path.join(plot_dir, "*.png"))
    
    if not png_files:
        print("❌ Nessun file PNG trovato!")
        return
    
    # Ordina per data di modifica (più recenti prima)
    png_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    
    print(f"📁 Ultimi 10 file modificati:")
    
    for i, file_path in enumerate(png_files[:10]):
        filename = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        mod_time = os.path.getmtime(file_path)
        
        import datetime
        mod_datetime = datetime.datetime.fromtimestamp(mod_time)
        
        print(f"   {i+1:2d}. {filename}")
        print(f"       Dimensione: {file_size:,} bytes")
        print(f"       Modificato: {mod_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if "evoluzione" in filename:
            print(f"       🎯 GRAFICO TEMPORALE")
        print()

if __name__ == "__main__":
    print("🚀 Verifica implementazione grafici temporali evoluzione punteggio")
    print("=" * 80)
    
    success = verify_score_evolution_files()
    show_recent_files()
    
    print("=" * 80)
    if success:
        print("🎉 VERIFICA COMPLETATA CON SUCCESSO!")
        print("   La funzionalità di grafici temporali è stata implementata correttamente.")
    else:
        print("⚠️  VERIFICA PARZIALE O FALLITA")
        print("   Controllare i messaggi sopra per i dettagli.")
    print("=" * 80)
