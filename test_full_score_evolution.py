#!/usr/bin/env python3
"""
Test completo per verificare la generazione dei grafici temporali
dell'evoluzione del punteggio durante un'esecuzione completa dell'applicazione.
"""

import os
import sys
import time
import queue
import logging
import threading

# Aggiungi il percorso del progetto al PYTHONPATH
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_full_execution_with_score_evolution():
    """Test completo dell'esecuzione con generazione dei grafici temporali."""
    
    print("🚀 Test completo con evoluzione del punteggio")
    print("=" * 60)
    
    try:
        from app.utils import generate_connected_random_graph
        from app.algorithms import test_instance
        from app.gui import DCSTApp
        
        print("✅ Import delle funzioni riuscito")
        
        # Simula un'esecuzione completa con istanze di test
        instances = {
            "piccola": 8,
            "media": 12,
            "grande": 16
        }
        
        max_children = 3
        penalty = 1000
        p_val = 0.4
        
        print(f"📊 Configurazione test:")
        print(f"   Istanze: {instances}")
        print(f"   Max children: {max_children}")
        print(f"   Penalty: {penalty}")
        print(f"   Probabilità connessione: {p_val}")
        
        # Crea una coda per i messaggi
        test_queue = queue.Queue()
        
        results = {}
        
        print("\n🔥 Esecuzione test per tutte le istanze...")
        
        for instance_name, n_nodes in instances.items():
            print(f"\n📈 Processando istanza {instance_name} ({n_nodes} nodi)...")
            
            # Genera il grafo
            G = generate_connected_random_graph(n_nodes, p_val)
            print(f"   Grafo generato: {len(G.nodes())} nodi, {len(G.edges())} archi")
            
            # Esegui il test
            start_time = time.time()
            instance_results = test_instance(
                G=G,
                max_children=max_children,
                penalty=penalty,
                instance_name=instance_name,
                stop_event=None,
                queue=test_queue,
                progress_info=None
            )
            end_time = time.time()
            
            results[instance_name] = instance_results
            
            print(f"   ✅ Completato in {end_time - start_time:.2f} secondi")
            
            # Verifica gli storici dei punteggi
            local_history = instance_results.get("local_score_history", [])
            sa_history = instance_results.get("sa_score_history", [])
            
            print(f"   📊 Local Search: {len(local_history)} punti dati")
            print(f"   📊 Simulated Annealing: {len(sa_history)} punti dati")
        
        # Test della generazione dei grafici
        print("\n🎨 Test generazione grafici temporali...")
        
        from app.utils import plot_score_evolution
        
        # Crea la cartella Plot se non esiste
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        plot_dir = os.path.join(desktop_path, "Plot")
        os.makedirs(plot_dir, exist_ok=True)
        
        generated_files = []
        
        for instance_name, instance_results in results.items():
            score_histories = {}
            
            if "local_score_history" in instance_results and instance_results["local_score_history"]:
                score_histories["Local Search"] = instance_results["local_score_history"]
            
            if "sa_score_history" in instance_results and instance_results["sa_score_history"]:
                score_histories["Simulated Annealing"] = instance_results["sa_score_history"]
            
            if score_histories:
                filename = f"test_full_evoluzione_{instance_name}_k{max_children}_p{p_val:.2f}.png"
                print(f"   📊 Generando grafico per istanza {instance_name}...")
                
                success = plot_score_evolution(score_histories, filename)
                
                if success:
                    full_path = os.path.join(plot_dir, filename)
                    if os.path.exists(full_path):
                        file_size = os.path.getsize(full_path)
                        print(f"   ✅ Grafico salvato: {filename} ({file_size} bytes)")
                        generated_files.append(filename)
                    else:
                        print(f"   ❌ File non trovato: {filename}")
                else:
                    print(f"   ❌ Errore nella generazione: {filename}")
        
        # Riepilogo finale
        print(f"\n📋 RIEPILOGO FINALE:")
        print(f"   Istanze processate: {len(results)}")
        print(f"   Grafici generati: {len(generated_files)}")
        
        for filename in generated_files:
            print(f"   ✅ {filename}")
        
        # Verifica che tutti i grafici siano stati generati
        expected_files = len([name for name, res in results.items() 
                            if res.get("local_score_history") or res.get("sa_score_history")])
        
        if len(generated_files) == expected_files:
            print(f"\n🎉 SUCCESSO! Tutti i {expected_files} grafici sono stati generati correttamente.")
            return True
        else:
            print(f"\n⚠️  PARZIALE: {len(generated_files)}/{expected_files} grafici generati.")
            return False
        
    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """Test dell'integrazione con la GUI (senza avviare l'interfaccia)."""
    
    print("\n🖥️  Test integrazione GUI")
    print("=" * 60)
    
    try:
        from app.gui import DCSTApp
        import tkinter as tk
        
        print("✅ Import GUI riuscito")
        
        # Crea una root window nascosta per il test
        root = tk.Tk()
        root.withdraw()  # Nasconde la finestra
        
        # Crea l'app
        app = DCSTApp(root)
        
        # Verifica che i metodi necessari esistano
        required_methods = ['save_results', 'save_partial_results']
        
        for method_name in required_methods:
            if hasattr(app, method_name):
                print(f"   ✅ Metodo {method_name} presente")
            else:
                print(f"   ❌ Metodo {method_name} mancante")
                return False
        
        # Verifica che l'import della funzione plot_score_evolution funzioni
        try:
            from app.utils import plot_score_evolution
            print("   ✅ Import plot_score_evolution nella GUI funziona")
        except ImportError as e:
            print(f"   ❌ Errore import plot_score_evolution: {e}")
            return False
        
        root.destroy()
        print("✅ Test integrazione GUI completato")
        return True
        
    except Exception as e:
        print(f"❌ Errore nel test GUI: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Configura il logging
    logging.basicConfig(level=logging.INFO)
    
    print("🚀 Test completo della funzionalità di evoluzione del punteggio")
    print("=" * 80)
    
    # Esegui i test
    test1_success = test_full_execution_with_score_evolution()
    test2_success = test_gui_integration()
    
    print("\n" + "=" * 80)
    print("📋 RISULTATI FINALI:")
    print(f"   Test esecuzione completa: {'✅ SUCCESSO' if test1_success else '❌ FALLITO'}")
    print(f"   Test integrazione GUI:    {'✅ SUCCESSO' if test2_success else '❌ FALLITO'}")
    
    if test1_success and test2_success:
        print("\n🎉 TUTTI I TEST SONO STATI SUPERATI!")
        print("   La funzionalità di evoluzione del punteggio è completamente operativa.")
        print("   I grafici temporali verranno generati automaticamente durante l'esecuzione.")
    else:
        print("\n⚠️  ALCUNI TEST SONO FALLITI")
        print("   Controllare i messaggi di errore sopra.")
    
    print("=" * 80)
