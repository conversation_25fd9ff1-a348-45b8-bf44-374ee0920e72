#!/usr/bin/env python3
"""
Test completo dell'esecuzione del DCST Tool con le modifiche plateau/reheat
"""

import sys
import os
import time
import threading
import tkinter as tk
from tkinter import ttk

# Aggiungi la directory corrente al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.gui import App

def automated_test_execution():
    """Test automatizzato che simula l'uso completo dell'app"""
    
    print("🚀 AVVIO TEST COMPLETO DCST TOOL")
    print("=" * 60)
    
    # Crea la GUI
    root = tk.Tk()
    root.title("DCST Tool - Test Automatizzato")
    
    progress_bar = ttk.Progressbar(root, orient="horizontal", length=300, mode="determinate")
    app = App(root, progress_bar)
    
    def run_automated_test():
        """Esegue il test automatizzato"""
        time.sleep(2)  # Aspetta che la GUI si inizializzi
        
        print("🔧 Configurazione parametri di test...")
        
        # Imposta parametri per un test veloce ma significativo
        app.nodes_var.set(15)  # Istanza piccola
        app.max_children_var.set(3)
        app.penalty_var.set(1000)
        app.connection_prob_var.set(0.35)
        
        print(f"   • Nodi: {app.nodes_var.get()}")
        print(f"   • Max figli: {app.max_children_var.get()}")
        print(f"   • Penalità: {app.penalty_var.get()}")
        print(f"   • Prob. connessione: {app.connection_prob_var.get()}")
        
        time.sleep(1)
        
        print("\n🎯 Avvio ottimizzazione...")
        print("📊 Monitoraggio valori plateau e reheat in tempo reale...")
        
        # Simula il click del pulsante "Avvia"
        app.start_optimization()
        
        # Monitora l'esecuzione
        start_time = time.time()
        last_plateau = None
        last_reheat = None
        plateau_changes = 0
        reheat_changes = 0
        
        while app.optimization_running:
            time.sleep(0.5)
            
            # Leggi i valori correnti dalle etichette
            plateau_text = app.plateau_label.cget("text")
            reheat_text = app.reheat_label.cget("text")
            
            # Estrai i valori numerici
            try:
                current_plateau = int(plateau_text.split(": ")[1]) if ": " in plateau_text else 0
                current_reheat = int(reheat_text.split(": ")[1]) if ": " in reheat_text else 0
                
                # Conta i cambiamenti
                if last_plateau is not None and current_plateau != last_plateau:
                    plateau_changes += 1
                if last_reheat is not None and current_reheat != last_reheat:
                    reheat_changes += 1
                
                last_plateau = current_plateau
                last_reheat = current_reheat
                
                # Stampa aggiornamenti periodici
                elapsed = time.time() - start_time
                if int(elapsed) % 5 == 0 and elapsed > 0:
                    print(f"   ⏱️  {elapsed:.0f}s - Plateau: {current_plateau}, Reheat: {current_reheat}")
                
            except (ValueError, IndexError):
                pass
            
            # Timeout di sicurezza
            if time.time() - start_time > 60:  # 1 minuto max
                print("⏰ Timeout raggiunto, interrompo il test")
                break
        
        # Risultati finali
        print(f"\n📈 RISULTATI DEL TEST:")
        print(f"   • Durata esecuzione: {time.time() - start_time:.1f} secondi")
        print(f"   • Cambiamenti plateau: {plateau_changes}")
        print(f"   • Cambiamenti reheat: {reheat_changes}")
        print(f"   • Plateau finale: {last_plateau}")
        print(f"   • Reheat finale: {last_reheat}")
        
        # Verifica che i valori siano stati aggiornati
        success = True
        if plateau_changes == 0:
            print("⚠️  ATTENZIONE: Nessun cambiamento nei valori plateau rilevato")
            success = False
        else:
            print("✅ Valori plateau aggiornati correttamente")
        
        if last_plateau is None or last_plateau < 0:
            print("❌ ERRORE: Valori plateau non validi")
            success = False
        else:
            print("✅ Valori plateau validi")
        
        if last_reheat is None or last_reheat < 0:
            print("❌ ERRORE: Valori reheat non validi")
            success = False
        else:
            print("✅ Valori reheat validi")
        
        # Controlla i log
        log_content = app.log_text.get("1.0", tk.END)
        plateau_logs = log_content.count("[SA] Plateau:")
        reheat_logs = log_content.count("Reheating applied")
        
        print(f"   • Log con plateau/reheat: {plateau_logs}")
        print(f"   • Log di reheating: {reheat_logs}")
        
        if plateau_logs > 0:
            print("✅ Log periodici plateau/reheat presenti")
        else:
            print("⚠️  Nessun log periodico plateau/reheat trovato")
        
        print(f"\n{'🎉 TEST SUPERATO!' if success else '⚠️  TEST PARZIALMENTE SUPERATO'}")
        
        # Chiudi l'app dopo 2 secondi
        root.after(2000, root.quit)
    
    # Avvia il test in un thread separato
    test_thread = threading.Thread(target=run_automated_test, daemon=True)
    test_thread.start()
    
    # Aggiungi istruzioni nella GUI
    instructions = tk.Label(
        root,
        text="🧪 TEST AUTOMATIZZATO IN CORSO\n\nOsserva i valori di Plateau e Reheat\nnel pannello 'Parametri in tempo reale'",
        bg="#2b2b2b",
        fg="yellow",
        font=("Arial", 10, "bold"),
        justify="center"
    )
    instructions.pack(pady=10)
    
    try:
        # Avvia la GUI
        root.mainloop()
        print("\n✅ Test completato con successo!")
        
    except KeyboardInterrupt:
        print("\n⚠️  Test interrotto dall'utente")
    except Exception as e:
        print(f"\n❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("🏁 Test terminato")

if __name__ == "__main__":
    automated_test_execution()
